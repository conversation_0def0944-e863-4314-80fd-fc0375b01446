# frozen_string_literal: true

module Admin
  class PrescriptionsController < Admin::ApplicationController
    before_action :set_prescription, only: %i[edit update destroy show email download_pdf add_signature review finalize]

    def new
      @prescription = Prescription.new(patient_id: params[:patient_id])
    end

    def create
      @prescription = Prescription.new(prescription_params)
      @prescription.practice = Current.practice if @prescription.practice.nil?

      if @prescription.save
        # Redirect to the review page for signing
        redirect_to review_admin_prescription_path(@prescription)
      else
        render :new
      end
    end

    def review
      # This action shows the prescription for review and signing
      if @prescription.practice.nil?
        @prescription.practice = Current.practice
        @prescription.save
      end
    end

    def finalize
      # This action processes the signature data and marks the prescription as signed

      # Process signature data if provided
      if params[:signature_data].present?
        # Extract the base64 data from the data URL
        data_uri = params[:signature_data]
        encoded_image = data_uri.split(',')[1]
        decoded_image = Base64.decode64(encoded_image)

        # Create a temp file with the decoded data
        temp_file = Tempfile.new(['signature', '.png'])
        temp_file.binmode
        temp_file.write(decoded_image)
        temp_file.rewind

        # Attach the signature to the prescription
        @prescription.signature_image.attach(io: temp_file, filename: "signature-#{Time.current.to_i}.png", content_type: 'image/png')
        temp_file.close
        temp_file.unlink

        # Mark as signed with timestamp
        @prescription.signed_at = Time.current
        @prescription.save

        generate_and_save_signed_pdf

        @prescription.create_patient_asset

        flash[:success] = 'Prescription signed and finalized successfully'
      else
        flash[:notice] = 'Prescription saved without signature'
      end

      redirect_to admin_prescription_path(@prescription)
    end

    def edit; end

    def update
      was_signed = @prescription.signed?
      attached = attach_signature
      if @prescription.update(prescription_params) && !was_signed
        collect_device_info if attached
        # Generate and save PDF if signature was attached and prescription is now signed
        generate_and_save_signed_pdf if attached && @prescription.signed?
        flash[:success] = 'Prescription updated successfully'
        redirect_to prescriptions_admin_patient_path(@prescription.patient)
      else
        render :edit
      end
    end

    def destroy
      @prescription.destroy
      flash[:success] = 'Prescription deleted successfully'
      redirect_to prescriptions_admin_patient_path(@prescription.patient)
    end

    def show
      # For the modern HTML view
      render 'show'
    end

    def download_pdf
      # Ensure practice is set for PDF generation
      if @prescription.practice.nil?
        @prescription.practice = Current.practice
        @prescription.save
      end

      respond_to do |format|
        format.html { redirect_to download_pdf_admin_prescription_path(@prescription, format: :pdf) }
        format.pdf do
          render pdf: "prescription-#{@prescription.id}",
                 template: 'admin/prescriptions/pdf',
                 layout: false,
                 margin: { top: 0, bottom: 0, left: 0, right: 0 },
                 page_size: 'A4',
                 disposition: 'inline'
        end
      end
    end

    def email
      if @prescription.patient.email.blank?
        flash[:error] = 'Cannot send prescription - patient has no email address'
        redirect_to prescriptions_admin_patient_path(@prescription.patient)
        return
      end

      PatientMailer.prescription(@prescription).deliver_now
      flash[:success] = 'Prescription sent successfully'
      redirect_to prescriptions_admin_patient_path(@prescription.patient)
    end

    def verify_auth
      # Get the dentist (either the current prescription's dentist or the current user)
      dentist_id = params[:dentist_id].presence || current_user.id
      dentist = User.find_by(id: dentist_id)

      return render json: { success: false, message: 'Dentist not found' } if dentist.blank?

      # Get the dentist's MFA method
      auth_value = params[:auth_value]
      mfa_method = dentist.mfa_method

      valid = case mfa_method
              when 'Password'
                # Verify password
                auth_value.present? && dentist.valid_password?(auth_value)
              when 'Pin', 'SMS'
                # Verify PIN - if no PIN is set, fall back to password
                if dentist.pin.present?
                  auth_value.present? && dentist.pin == auth_value
                else
                  # No PIN set, fall back to password verification
                  auth_value.present? && dentist.valid_password?(auth_value)
                end
              else
                # Default to PIN if MFA method is not recognized, fall back to password if no PIN
                if dentist.pin.present?
                  auth_value.present? && dentist.pin == auth_value
                else
                  auth_value.present? && dentist.valid_password?(auth_value)
                end
              end

      render json: { success: valid }
    end

    def add_signature
      if params[:signature_data].present?
        # Extract the base64 data from the data URL
        data_uri = params[:signature_data]
        encoded_image = data_uri.split(',')[1]
        decoded_image = Base64.decode64(encoded_image)

        # Create a temp file with the decoded data
        temp_file = Tempfile.new(['signature', '.png'])
        temp_file.binmode
        temp_file.write(decoded_image)
        temp_file.rewind

        # Attach the signature to the prescription
        @prescription.signature_image.attach(io: temp_file, filename: "signature-#{@prescription.id}.png", content_type: 'image/png')
        temp_file.close
        temp_file.unlink

        # Mark the prescription as signed
        @prescription.update(signed_at: Time.current)

        generate_and_save_signed_pdf

        flash[:success] = 'Prescription signed successfully'
      else
        flash[:error] = 'No signature data provided'
      end

      redirect_to admin_prescription_path(@prescription)
    end

    private

    def set_prescription
      @prescription = Prescription.find(params[:id])
    end

    def attach_signature
      b64 = params[:signature_image]
      return false unless b64.present? && !@prescription.signature_image.attached?

      mime_type = b64.split(',').first.split(';').first.split(':').last
      data = b64.split(',').last
      decoded_data = Base64.decode64(data)
      extension = Rack::Mime::MIME_TYPES.invert[mime_type]
      filename = ['signature-', SecureRandom.uuid, extension].join
      @prescription.signature_image.attach(
        io: StringIO.new(decoded_data),
        filename: filename,
        content_type: mime_type
      )

      true
    end

    def collect_device_info
      browser = Browser.new(request.user_agent)
      @prescription.update(browser_data: {
                             ip: request.remote_ip,
                             user_agent: request.user_agent,
                             device_type: browser.device.mobile? ? 'Mobile' : 'Desktop',
                             os: browser.platform.name,
                             os_version: browser.platform.version,
                             browser: browser.name,
                             browser_version: browser.version
                           })
    end

    def prescription_params
      # Only extract these params if prescription already exists and is being updated
      params[:prescription].extract!(:dentist_id, :patient_id, :practice_id) if @prescription&.id.present?

      if @prescription&.signature_image&.attached? && (@prescription&.dentist&.id != current_user.id || @prescription&.signed?)
        params.extract!(:signature_image)
      end
      params.extract!(:signature_image) if params[:signature_image] == 'data:,'

      params.require(:prescription).permit(
        :patient_id,
        :dentist_id,
        :practice_id,
        :date,
        :details,
        prescription_medications_attributes: %i[
          id
          medication_id
          dosage
          days
          times_per_day
          _destroy
        ]
      )
    end

    def generate_and_save_signed_pdf
      # Generate PDF using the same template as download_pdf
      pdf_content = WickedPdf.new.pdf_from_string(
        render_to_string(
          template: 'admin/prescriptions/pdf',
          layout: false,
          formats: [:pdf]
        ),
        margin: { top: 0, bottom: 0, left: 0, right: 0 },
        page_size: 'A4'
      )

      # Attach the signed PDF to the prescription
      @prescription.signed_pdf.attach(
        io: StringIO.new(pdf_content),
        filename: "prescription-#{@prescription.id}-signed.pdf",
        content_type: 'application/pdf'
      )

      # Create a patient asset with the signed PDF
      asset = PatientAsset.new(
        patient: @prescription.patient,
        label: 'Prescription',
        assignable: @prescription
      )

      asset.file.attach(
        io: StringIO.new(pdf_content),
        filename: "prescription-#{@prescription.id}-signed.pdf",
        content_type: 'application/pdf'
      )

      if asset.save
        Rails.logger.info "Successfully created patient asset #{asset.id} for patient #{@prescription.patient.id}"
      else
        Rails.logger.error "Failed to create patient asset: #{asset.errors.full_messages.join(', ')}"
      end
    end
  end
end
