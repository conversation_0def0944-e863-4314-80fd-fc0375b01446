# frozen_string_literal: true

module Patients
  class VerificationController < ApplicationController
    include Devise::Controllers::Helpers
    layout 'patient_page'

    # Make sure we can access the current patient in the views
    helper_method :current_patient

    def current_patient
      @patient if defined?(@patient)
    end

    # Skip authentication for verification routes
    skip_before_action :authenticate_patient!, if: -> { defined?(authenticate_patient!) }
    skip_before_action :check_2fa, if: -> { defined?(check_2fa) }

    before_action :check_patient_info, except: :verify_email

    def options
      # Get the patient from the session
      @patient = Patient.find_by(id: session[:patient_info_verified])
      @practice = @patient&.practices&.first

      # Check if the patient has a password set
      @has_password = @patient&.encrypted_password.present? && !@patient&.encrypted_password&.empty?

      # Render the verification options page
      # The template is at app/views/patients/verify_options.html.erb
      render template: 'patients/verify_options'
    end

    def verify_sms
      @patient = Patient.find_by(id: session[:patient_info_verified])

      if @patient&.mobile_phone.present?
        practice = @patient.practices.first
        sinch_service = Sinch::VerificationService.new(practice)
        response = sinch_service.send_verification_code(@patient.mobile_phone)
        @patient.update(secure_code_sent_at: Time.zone.now)

        if response['id']
          render json: { success: true, verification_id: response['id'] }
        else
          render json: { success: false, error: response[:error] || 'Failed to send verification code' }
        end
      else
        render json: { success: false, error: 'No mobile phone number found for this patient' }
      end
    end

    def verify_email
      @patient = Patient.find_by(id: session[:patient_info_verified])

      if @patient.nil?
        render json: { success: false, error: 'Patient information not found' }
        return
      end

      if @patient.email.blank?
        render json: { success: false, error: 'No email address found for this patient' }
        return
      end

      if @patient.email.blank?
        render json: { success: false, error: 'No email address found for this patient' }
        return
      end

      verification_code = rand(100_000..999_999).to_s
      session[:email_verification_code] = verification_code
      session[:email_verification_expires_at] = 10.minutes.from_now.to_i
      @patient.update(secure_code_sent_at: Time.zone.now)

      begin
        PatientMailer.verification_code(@patient, verification_code).deliver_now
        render json: { success: true, verification_id: "email-verification-#{Time.now.to_i}" }
      rescue StandardError
        render json: { success: false, error: 'Failed to send verification email. Please try again.' }
      end
    end

    def verify_staff
      @patient = Patient.find_by(id: session[:patient_info_verified])

      unless @patient
        render json: { success: false, error: 'Patient information not found' }
        return
      end

      # Parse JSON request body if content type is application/json
      if request.content_type == 'application/json'
        params_data = JSON.parse(request.body.read)
        params.merge!(params_data.symbolize_keys)
      end

      email = params[:email]
      password = params[:password]

      # Find the staff member by email
      staff_member = User.find_by(email: email)

      if staff_member
        # If staff member exists but has no password set, allow verification without password
        if staff_member.encrypted_password.blank? || staff_member.encrypted_password == ''
          # Mark the patient as verified by staff
          session[:two_factor_verified_at] = Time.current
          session[:verified_by_staff_id] = staff_member.id

          @patient.update(witnessed_by_id: staff_member.id)
          # Skip password setup and sign in the patient directly
          sign_in(@patient)
          redirect_url = session.delete(:patient_return_to) || dashboard_patients_patients_path
          render json: {
            success: true,
            redirect_url: redirect_url
          }
        elsif staff_member.valid_password?(password)
          # Verify staff member with password
          session[:two_factor_verified_at] = Time.current
          session[:verified_by_staff_id] = staff_member.id

          @patient.update(witnessed_by_id: staff_member.id, verification_method: 'staff')
          # Skip password setup and sign in the patient directly
          sign_in(@patient)
          redirect_url = session.delete(:patient_return_to) || dashboard_patients_patients_path
          render json: {
            success: true,
            redirect_url: redirect_url
          }
        # Mark the patient as verified by staff
        else
          render json: { success: false, error: 'Invalid password' }
        end
      else
        render json: { success: false, error: 'Staff member not found' }
      end
    end

    def verify_identity
      # Get the patient from the session
      @patient = Patient.find_by(id: session[:patient_info_verified])

      unless @patient
        render json: { success: false, error: 'Patient not found' }, status: :not_found
        return
      end

      # Get the verification type from the params
      verification_type = params[:verification_type]

      case verification_type
      when '2fa'
        # Verify 2FA code
        verification_id = params[:verification_id]
        code = params[:code]

        # Use the Sinch service to verify the code
        practice = @patient.practices.first
        sinch_service = Sinch::VerificationService.new(practice)
        response = sinch_service.report_verification_code(verification_id, code)

        if response['status'] == 'SUCCESSFUL'
          # Mark the patient as verified
          session[:verified_patient_id] = @patient.id
          session[:two_factor_verified_at] = Time.current

          @patient.update(entered_into_portal_at: Time.zone.now)
          @patient.update(verification_method: '2fa')
          # Redirect to the set password page if no password is set, otherwise to dashboard
          if @patient.encrypted_password.present? && !@patient.encrypted_password.empty?
            sign_in(@patient)
            session.delete(:patient_info_verified)
            redirect_url = session.delete(:patient_return_to) || dashboard_patients_patients_path
            render json: { success: true, redirect_url: redirect_url }
          else
            render json: { success: true, redirect_url: patients_set_password_path }
          end
        else
          render json: { success: false, error: 'Invalid code' }, status: :unprocessable_entity
        end
      when 'email'
        # Verify email code
        code = params[:code]

        # Check if the code matches the one in the session
        if session[:email_verification_code].present? &&
           session[:email_verification_expires_at].present? &&
           Time.now.to_i < session[:email_verification_expires_at] &&
           session[:email_verification_code] == code

          # Mark the patient as verified
          session[:verified_patient_id] = @patient.id
          session[:two_factor_verified_at] = Time.current

          # Clear the email verification session data
          session.delete(:email_verification_code)
          session.delete(:email_verification_expires_at)

          @patient.update(entered_into_portal_at: Time.zone.now)
          @patient.update(verification_method: 'email')
          # Redirect to the set password page if no password is set, otherwise to dashboard
          if @patient.encrypted_password.present? && !@patient.encrypted_password.empty?
            sign_in(@patient)
            session.delete(:patient_info_verified)
            redirect_url = session.delete(:patient_return_to) || dashboard_patients_patients_path
            render json: { success: true, redirect_url: redirect_url }
          else
            render json: { success: true, redirect_url: patients_set_password_path }
          end
        else
          render json: { success: false, error: 'Invalid or expired code' }, status: :unprocessable_entity
        end
      when 'password'
        # Verify password
        password = params[:password]

        # Check if the password is correct
        if @patient.valid_password?(password)
          # Sign in the patient directly
          sign_in(@patient)

          # Mark as verified for 2FA
          session[:two_factor_verified_at] = Time.current

          @patient.update(entered_into_portal_at: Time.zone.now)
          @patient.update(verification_method: 'password')
          # Clear the session variables
          session.delete(:patient_info_verified)
          redirect_url = session.delete(:patient_return_to) || dashboard_patients_patients_path

          # Redirect to the patient dashboard
          render json: { success: true, redirect_url: redirect_url }
        else
          render json: { success: false, error: 'Invalid password' }, status: :unprocessable_entity
        end
      else
        render json: { success: false, error: 'Invalid verification type' }, status: :unprocessable_entity
      end
    end

    def set_password
      if session[:verified_patient_id].blank?
        redirect_to new_patient_session_path, alert: 'Please verify your identity first'
        return
      end

      @patient = Patient.find_by(id: session[:verified_patient_id])

      unless @patient
        redirect_to new_patient_session_path, alert: 'Patient not found'
        return
      end

      render :set_password
    end

    def enter_password
      if session[:patient_info_verified].blank?
        redirect_to new_patient_session_path, alert: 'Please enter your information first'
        return
      end

      @patient = Patient.find_by(id: session[:patient_info_verified])

      unless @patient
        redirect_to new_patient_session_path, alert: 'Patient not found'
        return
      end

      render :enter_password
    end

    def verify_password
      @patient = Patient.find_by(id: params[:patient_id])

      unless @patient
        redirect_to new_patient_session_path, alert: 'Patient not found'
        return
      end

      if @patient.valid_password?(params[:password])
        sign_in(@patient)
        redirect_url = session.delete(:patient_return_to) || dashboard_patients_patients_path
        session.delete(:patient_info_verified)
        redirect_to redirect_url
      else
        flash.now[:alert] = 'Invalid password'
        render :enter_password
      end
    end

    def save_password
      if session[:verified_patient_id].blank?
        redirect_to new_patient_session_path, alert: 'Please verify your identity first'
        return
      end

      @patient = Patient.find_by(id: session[:verified_patient_id])

      unless @patient
        redirect_to new_patient_session_path, alert: 'Patient not found'
        return
      end

      password = params[:password]
      password_confirmation = params[:password_confirmation]

      if password.blank?
        flash.now[:alert] = 'Password cannot be blank'
        render :set_password
        return
      end

      if password != password_confirmation
        flash.now[:alert] = 'Passwords do not match'
        render :set_password
        return
      end

      # Check for minimum length (8 characters)
      if password.length < 8
        flash.now[:alert] = 'Password must be at least 8 characters'
        render :set_password
        return
      end

      # Check for special characters
      special_chars = %r{[!@#$%^&*()_\-+=\[\]{}|\\:;"'<>,.?/~`]}
      unless password.match?(special_chars)
        flash.now[:alert] = 'Password must include at least one special character (e.g., !@#$%^&*)'
        render :set_password
        return
      end

      begin
        if @patient.update(password: password, password_confirmation: password)
          sign_in(@patient)
          session.delete(:verified_patient_id)
          redirect_url = session.delete(:patient_return_to) || dashboard_patients_patients_path
          redirect_to redirect_url, notice: 'Password set successfully'
        else
          flash.now[:alert] = "Failed to set password: #{@patient.errors.full_messages.join(', ')}"
          render :set_password
        end
      rescue StandardError => e
        flash.now[:alert] = "An error occurred: #{e.message}"
        render :set_password
      end
    end

    private

    def check_patient_info
      unless session[:patient_info_verified].present? || session[:verified_patient_id].present?
        redirect_to new_patient_session_path, alert: 'Please enter your information first'
        return false
      end
      true
    end
  end
end
