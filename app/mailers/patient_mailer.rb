# frozen_string_literal: true

class PatientMailer < ApplicationMailer
  default from: '<EMAIL>'
  layout 'patient_mailer'

  def email_message(user, subject, title, text, attachment = nil, sender = nil)
    return if user.email.blank?

    @patient = user
    @subject = subject || 'You have a New Message'
    @title = title || 'Message from Practice'
    @text = text

    @from = if sender
              sender.identifier
            else
              '<EMAIL>'
            end

    if attachment.present?
      attachments[attachment.filename.to_s] = {
        mime_type: 'application/pdf',
        content: attachment.download
      }
    end

    mail(from: @from, to: user.email, subject: @subject)
  end

  def document_signing_request(patient, request, url)
    return if patient.email.blank?

    @patient = patient
    @request = request
    @url = url

    mail(to: patient.email, subject: "Signature Requested: #{request.signable_document.title}")
  end

  def document_signing_complete(patient, request, document)
    return if patient.email.blank?

    @patient = patient
    @request = request
    @document = document

    attachments[document.signed_pdf.filename.to_s] = {
      mime_type: 'application/pdf',
      content: document.signed_pdf.download
    }

    mail(to: patient.email, subject: "Signature Complete: #{document.title}") do |format|
      format.html { render layout: false }
    end
  end

  def prescription(prescription)
    return if prescription.patient.email.blank?

    @patient = prescription.patient
    @prescription = prescription

    attachments[prescription.signed_pdf.filename.to_s] = {
      mime_type: 'application/pdf',
      content: prescription.signed_pdf.download
    }

    mail(to: prescription.patient.email, subject: 'New Prescription')
  end

  def verification_code(patient, code)
    return if patient.email.blank?

    @patient = patient
    @code = code

    mail(to: patient.email, subject: 'Your Verification Code')
  end
end
