<%= form_for [:admin_general_settings, user] do |form| %>
  <div class="p-6 md:p-8 bg-white rounded-lg shadow-sm border border-slate-200">
    <!-- BASIC DETAILS -->
    <h2 class="text-lg font-semibold text-slate-800 mb-4">Basic details</h2>
    <div class="space-y-6 mb-8">
      <!-- Title - Full width -->
      <div>
        <%= form.label :title, class: "block text-sm font-medium text-slate-700 mb-1" %>
        <%= form.select :title, ["Mr", "Mrs", "Miss", "Ms", "Dr", "Master", "Prof", "<PERSON>", "<PERSON>", "Sir", "Lady", "Lord", "<PERSON>", "<PERSON>", "<PERSON>"], {}, class: "flex h-10 w-full rounded-md border border-[#e4e4e7] bg-background px-3 py-2 text-sm placeholder:text-muted-foreground focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2" %>
      </div>

      <!-- First name and Last name - Side by side -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <%= form.label :first_name, "First name", class: "block text-sm font-medium text-slate-700 mb-1" %>
          <%= form.text_field :first_name, class: "flex h-10 w-full rounded-md border border-[#e4e4e7] bg-background px-3 py-2 text-sm placeholder:text-muted-foreground focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2", required: true %>
        </div>
        <div>
          <%= form.label :last_name, "Last name", class: "block text-sm font-medium text-slate-700 mb-1" %>
          <%= form.text_field :last_name, class: "flex h-10 w-full rounded-md border border-[#e4e4e7] bg-background px-3 py-2 text-sm placeholder:text-muted-foreground focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2", required: true %>
        </div>
      </div>

      <!-- GDC number - Full width -->
      <div>
        <%= form.label :gdc_number, "GDC number", class: "block text-sm font-medium text-slate-700 mb-1" %>
        <%= form.text_field :gdc_number, class: "flex h-10 w-full rounded-md border border-[#e4e4e7] bg-background px-3 py-2 text-sm placeholder:text-muted-foreground focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2" %>
      </div>

      <!-- Online booking description and Profile image - 70/30 split -->
      <div class="flex flex-col lg:flex-row gap-6">
        <div class="lg:w-2/3">
          <%= form.label :online_booking_description, "Biography", class: "block text-sm font-medium text-slate-700 mb-1" %>
          <%= form.text_area :online_booking_description, placeholder: "Enter a description that will be shown to patients during online booking...", rows: 8, class: "flex w-full rounded-md border border-[#e4e4e7] bg-background px-3 py-2 text-sm placeholder:text-muted-foreground focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 resize-none" %>
        </div>
        <div class="lg:w-1/3">
          <label class="block text-sm font-medium text-slate-700 mb-1">Profile Image</label>
          <div class="flex flex-col items-center justify-center w-full">
            <%= form.label :image, for: "profileImage", class: "group relative flex flex-col items-center justify-center w-full h-48 border-2 border-dashed rounded-lg transition-colors cursor-pointer hover:bg-slate-100 border-slate-300 bg-slate-50 overflow-hidden" do %>
              <div class="flex flex-col items-center justify-center pt-5 pb-6 text-center" id="upload-placeholder" style="<%= user.image&.attached? ? 'display: none;' : '' %>">
                <svg class="lucide lucide-cloud-upload w-8 h-8 mb-4 text-slate-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path d="M4 14.899A7 7 0 1 1 15.71 8h1.79a4.5 4.5 0 0 1 2.5 8.242"></path>
                  <path d="M12 12v9"></path>
                  <path d="m16 16-4-4-4 4"></path>
                </svg>
                <p class="mb-2 text-sm text-slate-500"><span class="font-semibold">Upload Image</span></p>
              </div>

              <img id="previewImage" src="<%= url_for(user.image) if user.image&.attached? %>"
                   class="absolute inset-0 object-cover w-full h-full transition-opacity duration-200 <%= user.image&.attached? ? '' : 'hidden' %>" />
            <% end %>

            <%= form.file_field :image, accept: "image/png,image/jpeg,image/gif", class: "hidden", id: "profileImage" %>
          </div>
        </div>
      </div>
    </div>

    <!-- CONTACT DETAILS -->
    <h2 class="text-lg font-semibold text-slate-800 mb-4">Contact details</h2>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
      <div>
        <%= form.label :email, class: "block text-sm font-medium text-slate-700 mb-1" %>
        <%= form.email_field :email, class: "flex h-10 w-full rounded-md border border-[#e4e4e7] bg-background px-3 py-2 text-sm focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2", required: true %>
      </div>
      <div>
        <%= form.label :mobile_phone, "Mobile phone", class: "block text-sm font-medium text-slate-700 mb-1" %>
        <%= form.telephone_field :mobile_phone, class: "flex h-10 w-full rounded-md border border-[#e4e4e7] bg-background px-3 py-2 text-sm focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2" %>
      </div>
    </div>

    <% unless user.persisted? %>
      <!-- PASSWORD SETUP INFO FOR NEW USERS -->
      <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-8">
        <div class="flex items-center">
          <i class="fas fa-envelope text-blue-600 mr-3"></i>
          <div>
            <h4 class="text-sm font-medium text-blue-800">Password Setup</h4>
            <p class="text-sm text-blue-700 mt-1">The user will receive an email with instructions to set up their password after account creation.</p>
          </div>
        </div>
      </div>
    <% end %>

    <!-- SUBMIT BUTTON -->
    <div class="flex justify-end mt-8">
      <%= form.submit user.persisted? ? 'Save' : 'Create User', class: "inline-flex items-center justify-center rounded-md bg-blue-600 text-white px-4 py-2 text-sm font-medium hover:bg-blue-700 focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" %>
    </div>
  </div>
<% end %>
