<!-- BASIC DETAILS -->
<h2 class="text-lg font-semibold text-slate-800 mb-4">Basic details</h2>
<div class="space-y-6 mb-8">
  <!-- Title - Full width -->
  <div>
    <%= f.label :title, class: "block text-sm font-medium text-slate-700 mb-1" %>
    <%= f.text_field :title, value: @user.title, class: "flex h-10 w-full rounded-md border border-[#e4e4e7] bg-background px-3 py-2 text-sm placeholder:text-muted-foreground focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2" %>
  </div>

  <!-- First name and Last name - Side by side -->
  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
    <div>
      <%= f.label :first_name, "First name", class: "block text-sm font-medium text-slate-700 mb-1" %>
      <%= f.text_field :first_name, value: @user.first_name, class: "flex h-10 w-full rounded-md border border-[#e4e4e7] bg-background px-3 py-2 text-sm placeholder:text-muted-foreground focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2" %>
    </div>
    <div>
      <%= f.label :last_name, "Last name", class: "block text-sm font-medium text-slate-700 mb-1" %>
      <%= f.text_field :last_name, value: @user.last_name, class: "flex h-10 w-full rounded-md border border-[#e4e4e7] bg-background px-3 py-2 text-sm placeholder:text-muted-foreground focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2" %>
    </div>
  </div>

  <!-- GDC number - Full width -->
  <div>
    <%= f.label :gdc_number, "GDC number", class: "block text-sm font-medium text-slate-700 mb-1" %>
    <%= f.text_field :gdc_number, value: @user.gdc_number, class: "flex h-10 w-full rounded-md border border-[#e4e4e7] bg-background px-3 py-2 text-sm placeholder:text-muted-foreground focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2" %>
  </div>

  <!-- Online booking description and Profile image - 70/30 split -->
  <div class="flex flex-col lg:flex-row gap-6">
    <div class="lg:w-2/3">
      <%= f.label :online_booking_description, "Biography", class: "block text-sm font-medium text-slate-700 mb-1" %>
      <%= f.text_area :online_booking_description, value: @user.online_booking_description, placeholder: "Enter a description that will be shown to patients during online booking...", rows: 8, class: "flex w-full rounded-md border border-[#e4e4e7] bg-background px-3 py-2 text-sm placeholder:text-muted-foreground focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 resize-none" %>
    </div>
    <div class="lg:w-1/3">
      <label class="block text-sm font-medium text-slate-700 mb-1">Profile Image</label>
      <div class="flex flex-col items-center justify-center w-full">
        <%= f.label :profile_image, for: "profileImage", class: "group relative flex flex-col items-center justify-center w-full h-48 border-2 border-dashed rounded-lg transition-colors cursor-pointer hover:bg-slate-100 border-slate-300 bg-slate-50 overflow-hidden" do %>
          <div class="flex flex-col items-center justify-center pt-5 pb-6 text-center" id="upload-placeholder" style="<%= @user.image.attached? ? 'display: none;' : '' %>">
            <svg class="lucide lucide-cloud-upload w-8 h-8 mb-4 text-slate-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path d="M4 14.899A7 7 0 1 1 15.71 8h1.79a4.5 4.5 0 0 1 2.5 8.242"></path>
              <path d="M12 12v9"></path>
              <path d="m16 16-4-4-4 4"></path>
            </svg>
            <p class="mb-2 text-sm text-slate-500"><span class="font-semibold">Upload Image</span></p>
          </div>

          <img id="previewImage" src="<%= url_for(@user.image) if @user.image&.attached? %>"
               class="absolute inset-0 object-cover w-full h-full transition-opacity duration-200 <%= @user.image&.attached? ? '' : 'hidden' %>" />
        <% end %>

        <%= f.file_field :image, accept: "image/png,image/jpeg,image/gif", class: "hidden", id: "profileImage" %>
      </div>
    </div>
  </div>
</div>
