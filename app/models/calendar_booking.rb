# frozen_string_literal: true

# == Schema Information
#
# Table name: calendar_bookings
#
#  id                         :bigint           not null, primary key
#  start_time                 :datetime         not null
#  end_time                   :datetime         not null
#  cancelled_at               :datetime
#  notes                      :string
#  status                     :integer          default("scheduled")
#  booking_type               :integer          default("treatment")
#  is_on_cancellation_list    :boolean
#  cancellation_list_criteria :jsonb
#  repeat_event               :boolean          default(FALSE)
#  repeat_days                :string           default([]), is an Array
#  alternating_event          :string
#  end_repeat_date            :date
#  repeat_indefinitely        :boolean          default(FALSE)
#  recurrent_identifier       :string
#  event_color                :string           default("#D8D8D8")
#  patient_id                 :bigint
#  practitioner_id            :bigint           not null
#  booked_by_id               :bigint           not null
#  practice_id                :bigint           not null
#  treatment_id               :bigint
#  created_at                 :datetime         not null
#  updated_at                 :datetime         not null
#  meeting_id                 :bigint
#  waiting_room_job_id        :string
#  late_cancellation          :boolean          default(FALSE)
#  import_id                  :string
#  import_source              :integer
#  import_data                :jsonb
#  booked_by_type             :string           default("User"), not null
#  payment_request_status     :integer          default("not_requested")
#  payment_request_amount     :decimal(10, 2)
#
class CalendarBooking < ApplicationRecord
  include Importable

  belongs_to :practice
  belongs_to :patient, optional: true # NOTE: optional for events
  belongs_to :practitioner, class_name: 'User'
  belongs_to :booked_by, polymorphic: true
  belongs_to :treatment, optional: true
  belongs_to :meeting, optional: true

  has_one :recall, dependent: :nullify

  has_many :calendar_booking_cancellations, dependent: :destroy, foreign_key: 'booking_id'
  has_many :charting_appointments, dependent: :nullify
  has_many :lab_works, through: :charting_appointments
  has_many :payments, dependent: :nullify
  has_many :calendar_booking_notes, dependent: :destroy
  has_many :public_notes, -> { where(note_type: 'public', visible: true).order(created_at: :desc) }, class_name: 'CalendarBookingNote'
  has_many :private_notes, -> { where(note_type: 'private', visible: true).order(created_at: :desc) }, class_name: 'CalendarBookingNote'

  enum status: {
    scheduled: 0,
    cancelled: 1,
    confirmation_requested: 2,
    confirmed: 3,
    attended: 4,
    in_surgery: 5,
    completed: 6,
    running_late: 7,
    fta: 8
  }

  enum booking_type: {
    treatment: 0, # NOTE: via charting_appointment
    event: 1,
    recall: 2
  }, _prefix: true

  enum payment_request_status: {
    not_requested: 0,
    unpaid: 1,
    paid: 2
  }, _prefix: true

  STATUSES_BEFORE_SURGERY = %i[scheduled confirmation_requested confirmed attended].freeze
  STATUSES_BEFORE_ATTENDED = %i[scheduled confirmation_requested confirmed].freeze

  scope :not_cancelled, -> { where.not(status: :cancelled) }
  scope :on_date, ->(date) { where('DATE(start_time) = ? OR DATE(end_time) = ?', date, date) }
  scope :on_or_after_date, ->(date) { where('DATE(start_time) >= ?', date) }
  scope :on_or_after_time, ->(time) { where('start_time >= ?', time) }
  scope :between_dates, lambda { |start_date, end_date|
                          where('DATE(start_time) >= ? AND DATE(end_time) <= ?', start_date, end_date)
                        }
  scope :cancellation_list, -> { where(is_on_cancellation_list: true).where.not(status: :cancelled) }
  scope :not_on_cancellation_list, -> { where(is_on_cancellation_list: [false, nil]) }
  scope :starting_now_and_not_in_surgery, lambda {
    where(status: statuses.values_at(*STATUSES_BEFORE_SURGERY))
      .where(start_time: Time.current.beginning_of_minute..Time.current.end_of_minute)
  }
  scope :starting_now_and_not_attended, lambda {
    where(status: statuses.values_at(*STATUSES_BEFORE_ATTENDED))
      .where(start_time: Time.current.beginning_of_minute..Time.current.end_of_minute)
  }
  scope :five_minutes_late_and_not_in_surgery, lambda {
    where(status: statuses.values_at(*STATUSES_BEFORE_SURGERY))
      .where(start_time: 5.minutes.ago.beginning_of_minute..5.minutes.ago.end_of_minute)
  }
  scope :no_show_15_minutes_late, lambda {
    where(status: statuses.values_at(*STATUSES_BEFORE_ATTENDED))
      .where(start_time: 15.minutes.ago.beginning_of_minute..15.minutes.ago.end_of_minute)
  }
  scope :past_end_still_in_surgery, lambda {
    in_surgery.where(end_time: 1.minute.ago.beginning_of_minute..1.minute.ago.end_of_minute)
  }
  scope :missing_notes_24h_after_end, lambda {
    left_outer_joins(charting_appointments: %i[appointment_notes charted_treatments])
      .where('calendar_bookings.end_time BETWEEN ? AND ?', 48.hours.ago, 24.hours.ago)
      .where(status: :completed)
      .group('calendar_bookings.id')
      .having('COUNT(DISTINCT appointment_notes.id) = 0')
      .having(<<~SQL.squish)
        COUNT(DISTINCT CASE
          WHEN charted_treatments.notes IS NOT NULL
           AND charted_treatments.notes != ''
          THEN charted_treatments.id
        END) = 0
      SQL
  }

  # REBUILD: remake with ransack
  scope :search_by_query, lambda { |query|
    return all if query.blank?

    sanitized_query = "%#{query.downcase}%"

    joins(:patient, :practitioner)
      .where(
        "LOWER(users.first_name || ' ' || users.last_name) LIKE :query
         OR LOWER(patients.first_name || ' ' || patients.last_name) LIKE :query
         OR to_char(calendar_bookings.start_time, \'DD Mon YYYY\') LIKE :query
         OR to_char(calendar_bookings.end_time, \'DD Mon YYYY\') LIKE :query",
        query: sanitized_query
      )
  }

  before_save :set_booking_type

  validates :start_time, presence: true
  validates :end_time, presence: true
  validate :start_time_before_end_time

  after_update :notify_patient_attended
  after_update :notify_patient_in_surgery
  after_update :notify_patient_running_late

  after_save :trigger_automations

  def notify_patient_attended
    return unless saved_change_to_status? && status == 'attended' && patient_id.present?

    recipients = all_related_users

    recipients.each do |recipient|
      patient_friendly_names = charting_appointments
                               .includes(charted_treatments: :treatment)
                               .flat_map(&:charted_treatments)
                               .map(&:treatment)
                               .compact
                               .map(&:patient_friendly_name)
                               .uniq
      Notification.create(
        recipient: recipient,
        title: 'Patient arrived for appointment',
        description: "#{patient.full_name} arrived for their appointment at #{start_time.strftime('%H:%M')} and is currently in the waiting room. Their appointment is scheduled to start in #{((start_time - Time.zone.now) / 60).round(0)} minutes.",
        data: { type: 'appointment_status', sender: patient.full_name,
                style: 'attended',
                duration: duration_in_minutes,
                start_time: start_time.strftime('%H:%M'),
                end_time: end_time.strftime('%H:%M'),
                treatment: patient_friendly_names },
        actions: [
          { text: 'View Calendar', primary: true, action: 'redirect', href: '/admin/calendar_bookings/staff_calendar' },
          { text: 'Mark Read', action: 'mark_as_read' },
          { text: 'Remind Me Later', action: 'remind_in' }
        ]
      )

      if patient.balance_status == 'Overdue'
        Notification.create(
          recipient: recipient,
          title: "#{patient.full_name} has an outstanding balance of £#{patient.balance}",
          description: "#{patient.full_name} has been marked as attended but has an outstanding balance of <strong>£#{patient.balance}</strong> on their account. Please collect payment before admitting the patient to surgery, unless advised otherwise by a senior team member.",
          data: { type: 'patient_finances', color: '#4D7CFE', sender: patient.full_name },
          actions: [
            { text: 'View Payment Details', primary: true, action: 'redirect', href: "/admin/patients/#{patient.id}/account" },
            { text: 'Mark Read', action: 'mark_as_read' },
            { text: 'Remind Me Later', action: 'remind_in' }
          ]
        )
      end

      if payment_request_status == 'unpaid'
        description = "#{patient.full_name} has been marked as attended and owes <strong>£#{payment_request_amount}</strong> for this appointment. Please collect payment before admitting the patient to surgery, unless advised otherwise by a senior team member."
        Notification.create(
          recipient: recipient,
          title: "£#{payment_request_amount} payment required for #{patient.full_name}’s appointment",
          description: description,
          data: { type: 'patient_finances', color: '#4D7CFE', sender: patient.full_name },
          actions: [
            { text: 'View Payment Details', primary: true, action: 'redirect', href: "/admin/patients/#{patient.id}/account" },
            { text: 'Mark Read', action: 'mark_as_read' },
            { text: 'Remind Me Later', action: 'remind_in' }
          ]
        )
      end

      if charting_appointments.joins(:signature_requests).where(signature_requests: { status: 'pending' }).exists?
        documents = SignatureRequest
                    .joins(:charting_appointment)
                    .where(status: 'pending', charting_appointment_id: charting_appointments.select(:id))
                    .includes(signable_document: :document_template)
                    .map { |sr| sr.signable_document&.document_template&.name || '' }
                    .compact
                    .uniq

        description = <<~DESC.strip
          #{patient.full_name} has not signed the following consent forms/documents:
          #{documents.map { |doc| "- #{doc}" }.join("\n")}

          It is imperative that these are signed before any treatment begins. Failure to do so may leave the practice vulnerable from a clinical, legal, and compliance perspective.
        DESC

        Notification.create(
          recipient: recipient,
          title: "Required document or consent not signed for #{patient.full_name}",
          description: description,
          data: { type: 'documents_and_consents', color: '#8B5CF6', sender: patient.full_name, avatar_url: patient.image&.url },
          actions: [
            { text: 'View Documents', primary: true, action: 'redirect', href: '/admin/signature_requests' },
            { text: 'Mark Read', action: 'mark_as_read' },
            { text: 'Remind Me Later', action: 'remind_in' }
          ]
        )
      end

      latest_history = patient.medical_histories.order(created_at: :desc)&.first
      if latest_history.nil? || latest_history.created_at < 48.hours.ago
        description = "#{patient.full_name} has not updated their medical history for today’s appointment. Please ensure this is completed before commencing any treatment."
        Notification.create(
          recipient: recipient,
          title: "#{patient.full_name} has not recently updated their medical history",
          description: description,
          data: { type: 'medical_history', color: '#FDBA74', sender: patient.full_name, avatar_url: patient.image&.url },
          actions: [
            { text: 'View Medical History', primary: true, action: 'redirect', href: "/admin/patients/#{patient.id}/medical_histories" },
            { text: 'Mark Read', action: 'mark_as_read' },
            { text: 'Remind Me Later', action: 'remind_in' }
          ]
        )
      end

      next unless treatment.present? &&
                  !treatment.calendar_reserved_block_types.exists?(indicates_new: true) &&
                  charting_appointments.joins(:course_of_treatment).where(course_of_treatments: { accepted: false }).exists?

      Notification.create(
        recipient: recipient,
        title: "Unsigned treatment plan for #{patient.full_name}",
        description: "#{patient.full_name} has been marked as attended for: <strong>#{treatment&.patient_friendly_name || 'Other'}</strong> today, but no treatment plan has been signed. Please ensure a signed treatment plan is in place before proceeding, as this is required for clinical, legal, and compliance purposes.",
        data: { type: 'treatment_plans',
                color: '#25D366',
                sender: patient.full_name,
                avatar_url: patient.image&.url },
        actions: [
          { text: 'View Treatment Plan', primary: true, action: 'redirect', href: '/admin/treatment_plans' },
          { text: 'Mark Read', action: 'mark_as_read' },
          { text: 'Remind Me Later', action: 'remind_in' }
        ]
      )
    end
  end

  def notify_patient_in_surgery
    return unless saved_change_to_status? && status == 'in_surgery' && patient_id.present?

    recipients = all_related_users

    recipients.each do |recipient|
      if MedicalHistoryAiAlert.joins(medical_history: :patient).where(patients: { id: patient.id }).exists?
        alerts = MedicalHistoryAiAlert
                 .joins(medical_history: :patient)
                 .where(patients: { id: patient.id })

        alerts.each do |alert|
          next if alert.alert_text.blank?

          description = <<~DESC.strip
            #{patient.full_name} has a critical medical history notes:
            - #{alert.alert_text}
          DESC

          Notification.create(
            recipient: recipient,
            title: "Medical history alert for #{patient.full_name}",
            description: description,
            data: {
              type: 'medical_history',
              color: '#FDBA74',
              sender: patient.full_name,
              avatar_url: patient.image&.url
            },
            actions: [
              { text: 'View Medical History', primary: true, action: 'redirect', href: "/admin/patients/#{patient.id}/medical_histories" },
              { text: 'Mark Read', action: 'mark_as_read' },
              { text: 'Remind Me Later', action: 'remind_in' }
            ]
          )
        end
      end

      if charting_appointments.joins(:signature_requests).where(signature_requests: { status: 'pending' }).exists?
        documents = SignatureRequest
                    .joins(:charting_appointment)
                    .where(status: 'pending', charting_appointment_id: charting_appointments.select(:id))
                    .includes(signable_document: :document_template)
                    .map { |sr| sr.signable_document&.document_template&.name || '' }
                    .compact
                    .uniq

        description = <<~DESC.strip
          <strong>URGENT!</strong> #{patient.full_name} has not signed the following consent forms/documents:
          #{documents.map { |doc| "- #{doc}" }.join("\n")}

          It is imperative that these are signed before any treatment begins. Failure to do so may leave the practice vulnerable from a clinical, legal, and compliance perspective.
        DESC
        Notification.create(
          recipient: recipient,
          title: "URGENT! #{patient.full_name} in surgery without signed consent or required document",
          description: description,
          data: { type: 'documents_and_consents', color: '#8B5CF6', sender: patient.full_name, avatar_url: patient.image&.url },
          actions: [
            { text: 'View Documents', primary: true, action: 'redirect', href: '/admin/signature_requests' },
            { text: 'Mark Read', action: 'mark_as_read' },
            { text: 'Remind Me Later', action: 'remind_in' }
          ]
        )
      end

      next unless treatment.present? &&
                  !treatment.calendar_reserved_block_types.exists?(indicates_new: true) &&
                  charting_appointments.joins(:course_of_treatment).where(course_of_treatments: { accepted: false }).exists?

      Notification.create(
        recipient: recipient,
        title: "URGENT! #{patient.full_name} in surgery without signed treatment plan",
        description: "<strong>URGENT!</strong> #{patient.full_name} has been marked as in surgery but no treatment plan has been signed. Please ensure a signed treatment plan is in place before proceeding with treatment, as this is required for clinical, legal, and compliance purposes.",
        data: { type: 'treatment_plans',
                color: '#25D366',
                sender: patient.full_name,
                avatar_url: patient.image&.url },
        actions: [
          { text: 'View Treatment Plan', primary: true, action: 'redirect', href: '/admin/treatment_plans' },
          { text: 'Mark Read', action: 'mark_as_read' },
          { text: 'Remind Me Later', action: 'remind_in' }
        ]
      )
    end
  end

  def notify_patient_running_late
    return unless saved_change_to_status? && status == 'running_late' && patient_id.present?

    recipients = all_related_users

    recipients.each do |recipient|
      name = patient.full_name
      message = <<~HTML.strip
        #{name} has confirmed they are running late for their appointment. If the patient's appointment is no longer viable please contact them urgently to reschedule.
      HTML

      Notification.create(
        recipient: recipient,
        title: "#{name} is running late",
        description: message,
        data: { type: 'appointment_update', sender: name },
        actions: [
          { text: 'View Calendar', primary: true, action: 'redirect', href: '/admin/calendar_bookings/staff_calendar' },
          { text: 'Mark Read', action: 'mark_as_read' },
          { text: 'Remind Me Later', action: 'remind_in' }
        ]
      )
    end
  end

  # TODO: move to service
  def cancel!
    ActiveRecord::Base.transaction do
      late_cancellation_window_hours =
        if practice.calendar_setting.nil?
          24
        else
          practice.calendar_setting.late_cancellation_window_hours
        end

      late_cancellation = ((start_time - DateTime.now) * 24).to_f < late_cancellation_window_hours.hours

      update!(
        status: :cancelled,
        cancelled_at: DateTime.now,
        late_cancellation:
      )

      charting_appointments.each do |ca|
        ca.update!(calendar_booking: nil)
      end
    end
  end

  def add_to_cancellation_list!(criteria)
    update!(is_on_cancellation_list: true, cancellation_list_criteria: criteria)
  end

  def overlaps?(other_start_time, other_end_time)
    start_time < other_end_time && end_time > other_start_time
  end

  def time_of_day_overlaps?(other_start_time, other_end_time)
    start_time.seconds_since_midnight <= other_end_time.seconds_since_midnight &&
      end_time.seconds_since_midnight >= other_start_time.seconds_since_midnight
  end

  def end_time_of_day
    end_time.in_time_zone.seconds_since_midnight
  end

  def start_time_of_day
    start_time.in_time_zone.seconds_since_midnight
  end

  def duration_in_minutes
    ((end_time - start_time).to_f / 60).round if start_time && end_time
  end

  def duration
    return nil unless start_time && end_time

    hours = ((end_time - start_time) / 1.hour).floor
    minutes = (((end_time - start_time) % 1.hour) / 1.minute).round

    if hours.positive? && minutes.positive?
      "#{hours}h #{minutes}m"
    elsif hours.positive?
      "#{hours}h"
    elsif minutes.positive?
      "#{minutes}m"
    else
      '0m'
    end
  end

  def formatted_start_time
    start_time.strftime('%H:%M')
  end

  def formatted_start_date
    start_time.strftime("%A, #{start_time.day.ordinalize} %B %Y")
  end

  def formatted_end_time
    end_time.strftime('%H:%M')
  end

  def formatted_time_with_date
    start_time.strftime('%H:%M, %d %b %Y')
  end

  def display_title
    if booking_type == 'event'
      notes.present? ? notes.truncate(16) : 'Event'
    else
      patient&.full_name.to_s
    end
  end

  def all_related_users
    (patient.assigned_staff.reject(&:clinician?) + [practitioner]).uniq
  end

  # TODO: validate patient and practitioner are different and have appropriate roles
  # TODO: validate start_time is before end_time, validate their presence

  # NOTE: temporary stub for form; TODO: remove
  def repeat_pattern; end
  def repeat_pattern=(value); end

  private

  def set_booking_type
    self.booking_type = :treatment unless booking_type
  end

  def start_time_before_end_time
    return unless start_time && end_time

    errors.add(:start_time, 'must be before end time') if start_time >= end_time
  end

  def trigger_automations
    if saved_changes[:id] # creation
      Automations::TriggerProcessor.call(model: self, event_type: 'calendar_booking_created')
    else # update
      Automations::TriggerProcessor.call(model: self, event_type: 'calendar_booking_updated')

      if saved_changes[:status]
        Automations::TriggerProcessor.call(model: self, event_type: 'calendar_booking_status_updated')

        if saved_changes[:status].last == 'cancelled'
          Automations::TriggerProcessor.call(model: self, event_type: 'calendar_booking_cancelled')
        end
      end

      if saved_changes[:start_time] || saved_changes[:end_time]
        Automations::TriggerProcessor.call(model: self, event_type: 'calendar_booking_date_updated')
      end
    end
  end
end
