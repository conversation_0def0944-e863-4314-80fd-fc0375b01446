# frozen_string_literal: true

# == Schema Information
#
# Table name: medical_histories
#
#  id                     :bigint           not null, primary key
#  patient_id             :bigint           not null
#  created_by_id          :bigint           not null
#  ai_summary             :text
#  our_comments           :text
#  patient_comments       :text
#  additional_information :text
#  ai_alerts_done         :boolean          default(FALSE)
#  ai_changes_done        :boolean          default(FALSE)
#  ai_summary_done        :boolean          default(FALSE)
#  created_at             :datetime         not null
#  updated_at             :datetime         not null
#  key                    :string
#  data                   :jsonb
#  import_id              :string
#  import_source          :integer
#  import_data            :jsonb
#
class MedicalHistory < ApplicationRecord
  include EventLoggable
  include Importable

  belongs_to :patient
  belongs_to :created_by, class_name: 'User'

  has_many :medical_history_ai_alerts, dependent: :destroy
  has_many :medical_history_ai_changes, dependent: :destroy

  has_many :medical_histories_medical_history_answers, dependent: :destroy

  has_one :patient_asset, dependent: :destroy

  before_create :set_key
  after_create :set_ai_data

  def set_key
    self.key = SecureRandom.hex(64)
  end

  def set_ai_data
    MedicalHistoryJob.perform_later(id)
  end

  def create_patient_asset
    return if patient_asset.present?

    asset = patient.patient_assets.new(label: 'Medical History', medical_history: self)

    patient_data = {
      id: patient.id,
      name: patient.full_name,
      date_of_birth: patient.date_of_birth&.strftime('%B %d, %Y'),
      initials: get_patient_initials(patient.full_name)
    }

    medical_history_data = {
      completion_date: created_at.strftime('%B %d, %Y')
    }

    questions_data = format_questions_for_modal(data || [])
    alerts_data = format_alerts_for_modal(self)

    alerts = alerts_data.select { |item| item[:type] == 'alert' }
    changes = alerts_data.select { |item| item[:type] == 'change' }

    formatted_data = {
      patient: patient_data,
      medical_history: medical_history_data,
      questions: questions_data,
      alerts: alerts,
      changes: changes
    }

    pdf_html = ApplicationController.render(
      template: 'admin/patients/medical_histories/comprehensive_pdf',
      layout: false,
      assigns: { formatted_data: formatted_data }
    )

    pdf_content = WickedPdf.new.pdf_from_string(pdf_html)

    asset.file.attach(
      io: StringIO.new(pdf_content),
      filename: "medical-history-#{id}.pdf",
      content_type: 'application/pdf'
    )

    asset.save
  end

  def snapshot_answers
    update_column(:data, build_snapshot)
  end

  def build_snapshot
    MedicalHistoryQuestion
      .where(medical_history_answer_id: nil)
      .where(practice_id: [nil, patient.current_practice_id])
      .order(:position)
      .map { |question| serialize_question(question) }
  end

  def serialize_question(question)
    answer_records = medical_histories_medical_history_answers
                     .where(medical_history_answer_id: question.medical_history_answers.select(:id))

    answer_value =
      case question.question_type
      when 'text'
        answer_records.first&.value.presence || 'N/A'
      else
        if answer_records.any?
          texts = answer_records.map { |br| br.medical_history_answer.answer }
          question.single_answer? ? texts.first : texts
        else
          'No'
        end
      end

    node = {
      'answer' => answer_value,
      'position' => question.position,
      'question' => question.question
    }

    follow_ups = answer_records.flat_map do |bridging|
      bridging.medical_history_answer
              .medical_history_questions
              .map { |sub_q| serialize_question(sub_q) }
    end

    node['follow_ups'] = follow_ups if follow_ups.any?
    node
  end

  def self.send_to_email(patient, sent_by)
    conversation = patient.conversation
    practice = patient.current_practice
    sender = practice.communication_accounts.where(account_type: 'email').first

    template = NotificationTemplate.find_by(
      practice: conversation.practice,
      default: true,
      delivery_method: 'email',
      name: 'Send Medical History Link'
    )

    subject = ::Notifications::PlaceholderConverter.convert(patient, template.subject)
    text    = ::Notifications::PlaceholderConverter.convert(patient, template.text)
    title   = ::Notifications::PlaceholderConverter.convert(patient, template.title)

    service = Conversations::MessageCreationService.new(
      conversation,
      sent_by.id,
      text,
      {
        subject: subject,
        title: title,
        sender_id: sender.id
      }
    )
    service.execute
  end

  def self.send_to_sms(patient, sent_by)
    conversation = patient.conversation
    practice = patient.current_practice
    sender = practice.communication_accounts.where(account_type: 'sms').first

    template = NotificationTemplate.find_by(
      practice: conversation.practice,
      default: true,
      delivery_method: 'sms',
      name: 'Send Medical History Link'
    )

    text = ::Notifications::PlaceholderConverter.convert(patient, template.text)

    service = Conversations::MessageCreationService.new(
      conversation,
      sent_by.id,
      text,
      {
        sender_id: sender.id
      }
    )
    service.execute
  end

  private

  def get_patient_initials(full_name)
    return '--' if full_name.blank?

    names = full_name.split(' ')
    if names.length >= 2
      "#{names.first[0]}#{names.last[0]}".upcase
    else
      names.first[0..1].upcase
    end
  end

  def format_questions_for_modal(questions_data)
    question_ids = medical_histories_medical_history_answers
                   .joins(:medical_history_answer)
                   .pluck('medical_history_answers.medical_history_question_id')
                   .uniq

    ai_alerts = medical_history_ai_alerts.where.not(question_id: nil).group(:question_id).count
    ai_changes = medical_history_ai_changes.where.not(question_id: nil).group(:question_id).count

    result = questions_data.map.with_index do |question_data, index|
      question_id = find_question_id_for_data(question_data, question_ids, index)

      {
        id: question_id,
        question: question_data['question'],
        selected_answer: question_data['answer'],
        details: question_data['details'],
        has_alert: question_id && ai_alerts[question_id].present?,
        has_change: question_id && ai_changes[question_id].present?,
        priority: calculate_question_priority(question_id, ai_alerts, ai_changes),
        followup_questions: format_followup_questions(question_data['follow_ups'] || [])
      }
    end

    result.sort_by { |q| [-q[:priority], q[:question]] }
  end

  def find_question_id_for_data(question_data, _question_ids, _fallback_index)
    questions = MedicalHistoryQuestion.all
    matched_question = questions.find { |q| q.question == question_data['question'] }

    matched_question&.id
  end

  def calculate_question_priority(question_id, ai_alerts, ai_changes)
    return 0 unless question_id

    priority = 0
    priority += 2 if ai_alerts[question_id].present?
    priority += 1 if ai_changes[question_id].present?
    priority
  end

  def format_followup_questions(followups)
    return [] if followups.blank?

    followups.map do |followup|
      {
        question: followup['question'],
        answer: followup['answer']
      }
    end
  end

  def format_alerts_for_modal(medical_history)
    items = []

    medical_history.medical_history_ai_alerts.includes(:medical_history_question).find_each do |alert|
      question_text = alert.medical_history_question&.question || 'Unknown Question'
      items << {
        id: alert.id,
        type: 'alert',
        title: question_text.truncate(50),
        description: alert.alert_text,
        question_id: alert.question_id
      }
    end

    medical_history.medical_history_ai_changes.includes(:medical_history_question).find_each do |change|
      question_text = change.medical_history_question&.question || 'Unknown Question'
      items << {
        id: change.id,
        type: 'change',
        title: question_text.truncate(50),
        description: change.change_text,
        question_id: change.question_id
      }
    end

    items
  end
end
