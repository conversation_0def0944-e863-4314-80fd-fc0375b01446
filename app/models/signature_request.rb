# frozen_string_literal: true

# == Schema Information
#
# Table name: signature_requests
#
#  id                       :bigint           not null, primary key
#  patient_id               :bigint           not null
#  signable_document_id     :bigint           not null
#  created_by_id            :bigint           not null
#  status                   :string           default("pending")
#  expires_at               :datetime
#  verification_method      :string
#  access_token             :string
#  security_questions_setup :boolean          default(FALSE)
#  completed_at             :datetime
#  ip_address               :string
#  user_agent               :string
#  device_type              :string
#  os_name                  :string
#  os_version               :string
#  browser_name             :string
#  browser_version          :string
#  sinch_fingerprint        :string
#  staff_override           :boolean          default(FALSE)
#  witnessed_by_id          :bigint
#  signature_type           :string
#  signed_name              :string
#  created_at               :datetime         not null
#  updated_at               :datetime         not null
#  notification_sent_at     :datetime
#  date_fields              :json
#  sent_by_id               :bigint
#  sent_at                  :datetime
#  viewed                   :boolean          default(FALSE)
#  due_by                   :datetime
#  charting_appointment_id  :bigint
#  template_text            :text
#  viewed_at                :datetime
#  signed_at                :datetime
#
class SignatureRequest < ApplicationRecord
  include EventLoggable

  belongs_to :patient
  belongs_to :charting_appointment, optional: true
  belongs_to :signable_document
  belongs_to :created_by, class_name: 'User'
  belongs_to :sent_by, class_name: 'User', optional: true
  belongs_to :witnessed_by, class_name: 'User', optional: true

  has_many :signature_verifications
  has_one_attached :signature_image

  validates :access_token, presence: true, uniqueness: true

  before_validation :generate_access_token, on: :create

  scope :pending, -> { where(status: 'pending').where('due_by > ? OR due_by IS NULL', Date.current) }
  scope :late, -> { where(status: 'pending').where('due_by < ?', Date.current) }

  def text
    template_text
  end

  def notifications_sent?
    notification_sent_at.present?
  end

  def signed?
    %w[completed closed].include?(status)
  end

  def verified?
    signature_verifications.where(successful: true).exists? ||
      (staff_override? && witnessed_by_id.present?)
  end

  private

  def generate_access_token
    self.access_token ||= SecureRandom.urlsafe_base64(32)
  end
end
